package com.example.GetElecronicVocuherSerial.utils;

import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;

import javax.net.ssl.SSLContext;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URI;
import java.security.KeyStore;

public class SSLUtils {

    public static CloseableHttpClient createCustomSSLClient(String keystorePath, String keystorePassword) {
        try {
            // Handle file: protocol URLs
            String actualPath = keystorePath;
            if (keystorePath.startsWith("file:")) {
                URI uri = new URI(keystorePath);
                actualPath = uri.getPath();
            }
            
            // Load the truststore
            KeyStore trustStore = KeyStore.getInstance("JKS");
            
            // Try multiple ways to load the truststore
            InputStream trustStoreStream = null;
            try {
                // First try as absolute path
                trustStoreStream = new FileInputStream(actualPath);
            } catch (Exception e) {
                // If that fails, try as resource from classpath
                trustStoreStream = SSLUtils.class.getClassLoader().getResourceAsStream(actualPath);
                if (trustStoreStream == null) {
                    // Try without leading slash
                    String resourcePath = actualPath.startsWith("/") ? actualPath.substring(1) : actualPath;
                    trustStoreStream = SSLUtils.class.getClassLoader().getResourceAsStream(resourcePath);
                }
                if (trustStoreStream == null) {
                    throw new RuntimeException("Cannot find truststore at: " + keystorePath + " (tried: " + actualPath + ")");
                }
            }
            
            trustStore.load(trustStoreStream, keystorePassword.toCharArray());
            trustStoreStream.close();

            // Create SSL context with the truststore
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(trustStore, null)
                    .build();

            // Create SSL socket factory
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    new String[]{"TLSv1.2", "TLSv1.3"},
                    null,
                    SSLConnectionSocketFactory.getDefaultHostnameVerifier()
            );

            // Create HTTP client with custom SSL
            return HttpClients.custom()
                    .setSSLSocketFactory(sslSocketFactory)
                    .build();

        } catch (Exception e) {
            throw new RuntimeException("Failed to create custom SSL HttpClient", e);
        }
    }
}
