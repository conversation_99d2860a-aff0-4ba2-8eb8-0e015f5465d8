pipeline {
    agent {
        kubernetes {
            cloud 'kubernetes_mdc1_robot_allure'
            label 'op-backend-agents-maven'
            idleMinutes 5
            defaultContainer 'jnlp'
            yaml """
apiVersion: v1
kind: Pod
metadata:
  name: k8-agent-containers
  namespace: jenkins
  labels:
    job: op-backend-agents
spec:
  serviceAccountName: jenkins-admin
  containers:
    - name: jnlp
      image: mdc1-sfcr.safaricomet.net/dev-tools/jenkins-inbound-agent:allure
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "900m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: maven
      image: mdc1-sfcr.safaricomet.net/dev-tools/maven-builder:offline
      command: ["tail", "-f", "/dev/null"]
      env:
        - name: MAVEN_OPTS
          value: "-Xmx512m"
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "800Mi"
          cpu: "120m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
        - name: workspace-volume
          mountPath: /home/<USER>/agent
  imagePullSecrets:
    - name: harbor-registry-secret
  volumes:
    - name: ca-cert
      configMap:
        name: safaricomet.net
        items:
          - key: ca-certificates.crt
            path: ca-certificates.crt
    - name: workspace-volume
      emptyDir: {}
"""
        }
    }

    environment {
        VERSION = "${BUILD_NUMBER}"
        PP = sh(
            returnStdout: true,
            script: 'echo ${JOB_NAME} | cut -d/ -f2 | xargs echo -n'
        ).trim()
        J_NAME = "${PP}-${BRANCH_NAME}:${VERSION}"
    }

    stages {
        stage('Notify GitLab') {
            steps {
                echo 'Notifying GitLab'
                script {
                    try {
                        updateGitlabCommitStatus name: 'build', state: 'pending'
                    } catch (Exception e) {
                        echo "GitLab notification failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Test Basic Functionality') {
            steps {
                echo 'Testing basic Jenkins agent functionality...'
                sh 'echo "Jenkins agent is working!"'
                sh 'java -version'
                sh 'pwd && ls -la'
                sh 'whoami'
                sh 'echo "Build number: ${BUILD_NUMBER}"'
                sh 'echo "Job name: ${JOB_NAME}"'
            }
        }

        stage('Check Repository') {
            steps {
                echo 'Checking repository contents...'
                sh 'ls -la'
                sh 'find . -name "*.java" | head -5 || echo "No Java files found"'
                sh 'find . -name "pom.xml" || echo "No pom.xml found"'
                sh 'find . -name "Dockerfile" || echo "No Dockerfile found"'
            }
        }

        stage('Test Maven Container') {
            steps {
                echo 'Testing Maven container...'
                container('maven') {
                    sh 'echo "Maven container is working!"'
                    sh 'mvn --version'
                    sh 'java -version'
                }
            }
        }

        stage('Build with Maven') {
            when {
                expression { fileExists('pom.xml') }
            }
            steps {
                echo 'Building with Maven...'
                container('maven') {
                    sh 'mvn clean compile -DskipTests'
                    sh 'mvn package -DskipTests'
                    sh 'ls -la target/'
                }
            }
        }
    }
    
    post {
        always {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'success'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
        failure {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'failed'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
    }
}
