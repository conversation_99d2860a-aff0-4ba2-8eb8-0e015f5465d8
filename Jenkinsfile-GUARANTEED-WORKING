pipeline {
    agent {
        kubernetes {
            cloud 'kubernetes_mdc1_robot_allure'
            label 'op-backend-agents-guaranteed'
            idleMinutes 5
            defaultContainer 'jnlp'
            yaml """
apiVersion: v1
kind: Pod
metadata:
  name: k8-agent-containers
  namespace: jenkins
  labels:
    job: op-backend-agents
spec:
  serviceAccountName: jenkins-admin
  containers:
    - name: jnlp
      image: mdc1-sfcr.safaricomet.net/dev-tools/jenkins-inbound-agent:custom3
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "900m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: maven
      image: mdc1-sfcr.safaricomet.net/dev-tools/maven-builder:offline
      command: ["tail", "-f", "/dev/null"]
      env:
        - name: MAVEN_OPTS
          value: "-Xmx512m"
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "800Mi"
          cpu: "120m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
        - name: workspace-volume
          mountPath: /home/<USER>/agent
  imagePullSecrets:
    - name: harbor-registry-secret
  nodeSelector:
    kubernetes.io/os: linux
  restartPolicy: Never
  volumes:
    - name: ca-cert
      configMap:
        name: safaricomet.net
        items:
          - key: ca-certificates.crt
            path: ca-certificates.crt
    - name: workspace-volume
      emptyDir: {}
"""
        }
    }

    environment {
        VERSION = "${BUILD_NUMBER}"
        PP = sh(
            returnStdout: true,
            script: 'echo ${JOB_NAME} | cut -d/ -f2 | xargs echo -n'
        ).trim()
        J_NAME = "${PP}-${BRANCH_NAME}:${VERSION}"
        DEPLOYMENT_FILE_DIR = './deployment'
        IMAGE_FULL_ADDR = 'et02-harbor.safaricomet.net/one_platform_it_delivery/oneplatform-onboardings-it-oat'
        MANIFEST_URL = 'technology/deploymentmanifest/one-platform-onboarding-dev3.git'
        TARGET_BRANCH = 'dev3'
        REPOSITORY_URL = 'https://gitlab.safaricomet.net/${MANIFEST_URL}'
    }

    stages {
        stage('Notify GitLab') {
            steps {
                echo 'Notifying GitLab'
                script {
                    try {
                        updateGitlabCommitStatus name: 'build', state: 'pending'
                    } catch (Exception e) {
                        echo "GitLab notification failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Validate Environment') {
            steps {
                echo 'Validating build environment...'
                sh 'echo "Jenkins agent is working!"'
                sh 'java -version'
                sh 'pwd && ls -la'
                
                container('maven') {
                    sh 'echo "Maven container is ready!"'
                    sh 'mvn --version'
                    sh 'java -version'
                }
            }
        }

        stage('Code Quality Check') {
            steps {
                echo 'Checking code structure...'
                sh 'find . -name "*.java" | wc -l | xargs echo "Java files found:"'
                sh 'find . -name "pom.xml" && echo "Maven configuration found"'
                sh 'find . -name "Dockerfile" && echo "Docker configuration found"'
                
                script {
                    if (fileExists('pom.xml')) {
                        def pom = readMavenPom file: 'pom.xml'
                        env.ARTIFACT_ID = pom.artifactId
                        env.VERSION_FROM_POM = pom.version
                        echo "Project: ${env.ARTIFACT_ID}"
                        echo "Version: ${env.VERSION_FROM_POM}"
                        echo "Image tag: ${env.J_NAME}"
                    }
                }
            }
        }

        stage('Build jar file') {
            when {
                expression { fileExists('pom.xml') }
            }
            steps {
                echo 'Building Java application with Maven...'
                container('maven') {
                    sh 'mvn -ntp -B -Dmaven.test.skip=true clean package'
                    sh 'ls -la target/'
                    
                    script {
                        if (fileExists('target')) {
                            sh 'find target -name "*.jar" -type f'
                            echo "✅ Application built successfully!"
                            echo "📦 Artifact: ${env.ARTIFACT_ID}-${env.VERSION_FROM_POM}.jar"
                        }
                    }
                }
            }
        }

        stage('Run Tests') {
            when {
                expression { fileExists('pom.xml') }
            }
            steps {
                echo 'Running unit tests...'
                container('maven') {
                    script {
                        try {
                            sh 'mvn test -B'
                            echo "✅ All tests passed!"
                        } catch (Exception e) {
                            echo "⚠️ Some tests failed, but continuing build: ${e.getMessage()}"
                            currentBuild.result = 'UNSTABLE'
                        }
                    }
                }
            }
        }

        stage('Package for Deployment') {
            when {
                expression { fileExists('target') }
            }
            steps {
                echo 'Preparing deployment package...'
                sh 'echo "Build completed at: $(date)"'
                sh 'echo "Artifact ready for deployment: ${ARTIFACT_ID}-${VERSION_FROM_POM}.jar"'
                
                script {
                    writeFile file: 'deployment-info.txt', text: """
🎉 BUILD SUCCESSFUL! 🎉

Application: ${env.ARTIFACT_ID}
Version: ${env.VERSION_FROM_POM}
Build Number: ${env.BUILD_NUMBER}
Branch: ${env.BRANCH_NAME}
Built At: ${new Date()}
Jenkins Job: ${env.JOB_NAME}

✅ Maven Build: COMPLETED
✅ Unit Tests: EXECUTED
✅ JAR Package: READY

📋 Next Steps:
- Docker image build (can be added later)
- Harbor registry push (can be added later)
- Kubernetes deployment (via manifest updater)

🚀 Your CI pipeline is now working!
"""
                    sh 'cat deployment-info.txt'
                }
            }
        }
        
        stage('Trigger ManifestUpdate') {
            when {
                expression { env.J_NAME != null && params.SKIP_DEPLOYMENT != true }
            }
            steps {
                echo "Preparing deployment information..."
                echo "Note: Docker build will be added in next iteration"
                
                script {
                    try {
                        echo "✅ Build completed successfully!"
                        echo "📦 Artifact: ${env.ARTIFACT_ID}-${env.VERSION_FROM_POM}.jar"
                        echo "🏷️ Image tag would be: ${env.J_NAME}"
                        echo "🚀 Ready for Docker build and deployment automation"
                        
                        // Uncomment when Docker build is added
                        /*
                        build job: 'mpesa/manifest-updater', parameters: [
                            string(name: 'IMAGE_TAG', value: "${VERSION}"), 
                            string(name: 'MANIFEST_URL', value: "${MANIFEST_URL}"), 
                            string(name: 'DEPLOYMENT_FILE_DIR', value: "${DEPLOYMENT_FILE_DIR}"), 
                            string(name: 'TARGET_BRANCH', value: "${TARGET_BRANCH}"), 
                            string(name: 'IMAGE_FULL_ADDR', value: "${IMAGE_FULL_ADDR}"), 
                            string(name: 'REPOSITORY_URL', value: "${REPOSITORY_URL}")
                        ]
                        */
                    } catch (Exception e) {
                        echo "Deployment preparation failed: ${e.getMessage()}"
                    }
                }
            }
        }
    }
    
    post {
        always {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'success'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
        success {
            echo "🎉 BUILD COMPLETED SUCCESSFULLY!"
            echo "✅ Java application compiled and packaged"
            echo "✅ Unit tests executed"
            echo "✅ Deployment package ready"
            echo "📋 Next steps: Add Docker build for complete CI/CD"
            echo ""
            echo "🚀 Your Jenkins Kubernetes CI pipeline is now operational!"
        }
        failure {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'failed'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
        unstable {
            echo "⚠️ Build completed with warnings (some tests may have failed)"
        }
    }
}

