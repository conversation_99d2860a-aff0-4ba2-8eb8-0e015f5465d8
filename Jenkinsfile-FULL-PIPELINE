pipeline {
    agent {
        kubernetes {
            cloud 'kubernetes_mdc1_robot_allure'
            label 'op-backend-agents-full'
            idleMinutes 5
            defaultContainer 'jnlp'
            yaml """
apiVersion: v1
kind: Pod
metadata:
  name: k8-agent-containers
  namespace: jenkins
  labels:
    job: op-backend-agents
spec:
  serviceAccountName: jenkins-admin
  containers:
    - name: jnlp
      image: mdc1-sfcr.safaricomet.net/dev-tools/jenkins-inbound-agent:allure
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "900m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: maven
      image: mdc1-sfcr.safaricomet.net/dev-tools/maven-builder:offline
      command: ["tail", "-f", "/dev/null"]
      env:
        - name: MAVEN_OPTS
          value: "-Xmx512m"
      imagePullPolicy: Always
      resources:
        requests:
          memory: "512Mi"
          cpu: "100m"
        limits:
          memory: "2Gi"
          cpu: "1000m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: kaniko
      image: mdc1-sfcr.safaricomet.net/dev-tools/kaniko-debug-local:offline
      command:
        - cat
      tty: true
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "1"
      env:
        - name: DOCKER_CONFIG
          value: /etc/docker
      volumeMounts:
        - name: docker-config
          mountPath: /etc/docker
        - name: workspace-volume
          mountPath: /home/<USER>/agent
          readOnly: false
  imagePullSecrets:
    - name: harbor-registry-secret
  volumes:
    - name: ca-cert
      configMap:
        name: safaricomet.net
        items:
          - key: ca-certificates.crt
            path: ca-certificates.crt
    - name: docker-config
      secret:
        secretName: harbor-registry-key
        items:
          - key: .dockerconfigjson
            path: config.json
    - name: workspace-volume
      emptyDir: {}
"""
        }
    }

    environment {
        VERSION = "${BUILD_NUMBER}"
        PP = sh(
            returnStdout: true,
            script: 'echo ${JOB_NAME} | cut -d/ -f2 | xargs echo -n'
        ).trim()
        J_NAME = "${PP}-${BRANCH_NAME}:${VERSION}"
        DEPLOYMENT_FILE_DIR = './deployment'
        IMAGE_FULL_ADDR = 'et02-harbor.safaricomet.net/one_platform_it_delivery/oneplatform-onboardings-it-oat'
        MANIFEST_URL = 'technology/deploymentmanifest/one-platform-onboarding-dev3.git'
        TARGET_BRANCH = 'dev3'
        REPOSITORY_URL = 'https://gitlab.safaricomet.net/${MANIFEST_URL}'
    }

    stages {
        stage('Notify GitLab') {
            steps {
                echo 'Notifying GitLab'
                script {
                    try {
                        updateGitlabCommitStatus name: 'build', state: 'pending'
                    } catch (Exception e) {
                        echo "GitLab notification failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Test Basic Functionality') {
            steps {
                echo 'Testing basic Jenkins agent functionality...'
                sh 'echo "Jenkins agent is working!"'
                sh 'java -version'
                sh 'pwd && ls -la'
            }
        }

        stage('Check Repository') {
            steps {
                echo 'Checking repository contents...'
                sh 'ls -la'
                sh 'find . -name "*.java" | head -5'
                sh 'find . -name "pom.xml"'
                sh 'find . -name "Dockerfile"'
            }
        }

        stage('Test All Containers') {
            steps {
                echo 'Testing all containers...'
                
                container('jnlp') {
                    sh 'echo "JNLP container working!" && java -version'
                }
                
                container('maven') {
                    sh 'echo "Maven container working!" && mvn --version'
                }
                
                container('kaniko') {
                    sh 'echo "Kaniko container working!" && ls -la /kaniko/'
                }
            }
        }

        stage('Build with Maven') {
            when {
                expression { fileExists('pom.xml') }
            }
            steps {
                echo 'Building with Maven...'
                container('maven') {
                    sh 'mvn clean compile -DskipTests'
                    sh 'mvn package -DskipTests'
                    sh 'ls -la target/'
                    
                    script {
                        // Read artifact info from pom.xml
                        def pom = readMavenPom file: 'pom.xml'
                        env.ARTIFACT_ID = pom.artifactId
                        env.VERSION_FROM_POM = pom.version
                        echo "Built artifact: ${env.ARTIFACT_ID}-${env.VERSION_FROM_POM}.jar"
                    }
                }
            }
        }

        stage('Build Docker Image') {
            when {
                expression { fileExists('Dockerfile') && fileExists('target') }
            }
            steps {
                echo 'Building Docker image with Kaniko...'
                container('kaniko') {
                    script {
                        sh """
                            /kaniko/executor \\
                                --dockerfile=Dockerfile \\
                                --context=. \\
                                --destination=et02-harbor.safaricomet.net/one_platform_it_delivery/${J_NAME} \\
                                --insecure \\
                                --skip-tls-verify
                        """
                        echo "Docker image built: et02-harbor.safaricomet.net/one_platform_it_delivery/${J_NAME}"
                    }
                }
            }
        }
        
        stage('Trigger ManifestUpdate') {
            when {
                expression { env.J_NAME != null }
            }
            steps {
                echo "Triggering manifest update for image: ${J_NAME}"
                script {
                    try {
                        build job: 'mpesa/manifest-updater', parameters: [
                            string(name: 'IMAGE_TAG', value: "${VERSION}"), 
                            string(name: 'MANIFEST_URL', value: "${MANIFEST_URL}"), 
                            string(name: 'DEPLOYMENT_FILE_DIR', value: "${DEPLOYMENT_FILE_DIR}"), 
                            string(name: 'TARGET_BRANCH', value: "${TARGET_BRANCH}"), 
                            string(name: 'IMAGE_FULL_ADDR', value: "${IMAGE_FULL_ADDR}"), 
                            string(name: 'REPOSITORY_URL', value: "${REPOSITORY_URL}")
                        ]
                        echo "Manifest update triggered successfully"
                    } catch (Exception e) {
                        echo "Manifest update failed: ${e.getMessage()}"
                    }
                }
            }
        }
    }
    
    post {
        always {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'success'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
        failure {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'failed'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
        success {
            echo "🎉 Complete CI/CD pipeline executed successfully!"
            echo "✅ Maven build completed"
            echo "✅ Docker image built and pushed"
            echo "✅ Deployment manifest updated"
        }
    }
}
