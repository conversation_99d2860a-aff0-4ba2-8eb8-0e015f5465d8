pipeline {
    agent {
        kubernetes {
            yaml """
apiVersion: v1
kind: Pod
metadata:
  name: k8-agent-containers
  namespace: jenkins
  labels:
    job: mpesa-k8-agents
spec:
  serviceAccountName: jenkins-admin
  containers:
    - name: jnlp
      image: mdc1-sfcr.safaricomet.net/dev-tools/jenkins-inbound-agent:custom3
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "900m"
      volumeMounts:
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: maven
      image: mdc1-sfcr.safaricomet.net/dev-tools/maven-builder:offline
      command: ["tail", "-f", "/dev/null"]
      env:
        - name: MAVEN_OPTS
          value: "-Xmx512m"
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "800Mi"
          cpu: "120m"
      volumeMounts:
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: kaniko
      image: mdc1-sfcr.safaricomet.net/dev-tools/kaniko-debug-local:offline
      command:
        - cat
      tty: true
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "1"
      env:
        - name: DOCKER_CONFIG
          value: /etc/docker
      volumeMounts:
        - name: docker-config
          mountPath: /etc/docker
        - name: workspace-volume
          mountPath: /home/<USER>/agent
          readOnly: false
  imagePullSecrets:
    - name: harbor-registry-secret
  volumes:
    - name: docker-config
      secret:
        secretName: harbor-registry-key
        items:
          - key: .dockerconfigjson
            path: config.json
    - name: workspace-volume
      emptyDir: {}
"""
        }
    }

    environment {
        IMAGE = readMavenPom().getArtifactId()
        VERSION = "${BUILD_NUMBER}"
        PP = sh(
            returnStdout: true,
            script: 'echo ${JOB_NAME} | cut -d/ -f2 | xargs echo -n'
        ).trim()
        LATEST_COMMIT = sh(
        script: 'git rev-parse HEAD',
        returnStdout: true
        ).trim()
        GIT_URL = sh(
        script: 'git config --get remote.origin.url | sed "s/\\.git$//"',
        returnStdout: true
        ).trim()
        COMMITTER_NAME = sh(
        script: 'git log -1 --pretty=format:"%an"',
        returnStdout: true
        ).trim()
        J_NAME = "${PP}-${BRANCH_NAME}:${VERSION}"
        DEPLOYMENT_FILE_DIR = './deployment'
        IMAGE_FULL_ADDR = 'et02-harbor.safaricomet.net/one_platform_it_delivery/oneplatform-onboardings-it-oat'
        MANIFEST_URL = 'technology/deploymentmanifest/one-platform-onboarding-dev3.git'
        TARGET_BRANCH = 'dev3'
        REPOSITORY_URL = 'https://gitlab.safaricomet.net/${MANIFEST_URL}'
    }

    stages {
        stage('Notify GitLab') {
            steps {
                echo 'Notifying GitLab'
                script {
                    try {
                        updateGitlabCommitStatus name: 'build', state: 'pending'
                    } catch (Exception e) {
                        echo "GitLab notification failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Test Containers') {
            steps {
                echo 'Testing container functionality...'
                
                container('jnlp') {
                    sh 'echo "JNLP container working!"'
                    sh 'java -version'
                    sh 'pwd && ls -la'
                }
                
                container('maven') {
                    sh 'echo "Maven container working!"'
                    sh 'mvn --version'
                }
                
                container('kaniko') {
                    sh 'echo "Kaniko container working!"'
                    sh 'ls -la /kaniko/ || echo "Kaniko directory not found"'
                }
            }
        }

         stage("Build jar file"){
            steps{
                container('maven'){                    
                sh 'mvn -ntp -B -Dmaven.test.skip=true clean package'          
                }
            }
        }

        stage('Build Docker Image') {
            steps {
                container('kaniko') {
                    script {
                        sh '''
                            /kaniko/executor --dockerfile `pwd`/Dockerfile \
                            --context `pwd` \
                            --destination=et02-harbor.safaricomet.net/one_platform_it_delivery/${J_NAME}  --insecure --skip-tls-verify
                        '''
                    }
                }
            }
        }
        
        stage('Trigger ManifestUpdate') {
            steps {
                echo "triggering updatemanifestjob"
                build job: 'mpesa/manifest-updater', parameters: [
                    string(name: 'IMAGE_TAG', value: "${VERSION}"), 
                    string(name: 'MANIFEST_URL', value: "${MANIFEST_URL}"), 
                    string(name: 'DEPLOYMENT_FILE_DIR', value: "${DEPLOYMENT_FILE_DIR}"), 
                    string(name: 'TARGET_BRANCH', value: "${TARGET_BRANCH}"), 
                    string(name: 'IMAGE_FULL_ADDR', value: "${IMAGE_FULL_ADDR}"), 
                    string(name: 'REPOSITORY_URL', value: "${REPOSITORY_URL}")
                ]
            }
        }
    }
    
    post {
        always {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'success'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
        failure {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'failed'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
    }
}
