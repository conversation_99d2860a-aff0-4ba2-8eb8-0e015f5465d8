pipeline {
    agent {
        kubernetes {
            yaml """
apiVersion: v1
kind: Pod
spec:
  serviceAccountName: jenkins-admin
  containers:
    - name: jnlp
      image: mdc1-sfcr.safaricomet.net/dev-tools/jenkins-inbound-agent:custom3
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "1Gi"
          cpu: "500m"
    - name: maven
      image: mdc1-sfcr.safaricomet.net/dev-tools/maven-builder:offline
      command: ["tail", "-f", "/dev/null"]
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "800Mi"
          cpu: "120m"
  imagePullSecrets:
    - name: harbor-registry-secret
"""
        }
    }

    stages {
        stage('Test Basic Functionality') {
            steps {
                echo 'Testing basic pod functionality...'
                
                container('jnlp') {
                    sh 'echo "JNLP container working!"'
                    sh 'java -version'
                    sh 'pwd'
                    sh 'ls -la'
                }
                
                container('maven') {
                    sh 'echo "Maven container working!"'
                    sh 'mvn --version'
                }
            }
        }
    }
}
