# Kyverno Policy Exception for Jenkins namespace
# This allows Jenkins agents to bypass certain policies

apiVersion: kyverno.io/v2beta1
kind: PolicyException
metadata:
  name: jenkins-agent-exception
  namespace: jenkins
spec:
  exceptions:
  - policyName: require-requests-limits
    ruleNames:
    - validate-resources
  - policyName: restrict-image-registries
    ruleNames:
    - validate-registries
  match:
  - any:
    - resources:
        kinds:
        - Pod
        namespaces:
        - jenkins
        names:
        - "agent-*"
        - "jenkins-*"

---
# Alternative: Update the existing policies to exclude Jenkins namespace
apiVersion: kyverno.io/v1
kind: ClusterPolicy
metadata:
  name: require-requests-limits-updated
spec:
  validationFailureAction: enforce
  background: true
  rules:
  - name: validate-resources
    match:
      any:
      - resources:
          kinds:
          - Pod
    exclude:
      any:
      - resources:
          namespaces:
          - jenkins
          - kube-system
          - kyverno
    validate:
      message: "CPU and memory resource requests and limits are required for containers."
      pattern:
        spec:
          containers:
          - name: "*"
            resources:
              requests:
                memory: "?*"
                cpu: "?*"
              limits:
                memory: "?*"
                cpu: "?*"

---
apiVersion: kyverno.io/v1
kind: ClusterPolicy
metadata:
  name: restrict-image-registries-updated
spec:
  validationFailureAction: enforce
  background: true
  rules:
  - name: validate-registries
    match:
      any:
      - resources:
          kinds:
          - Pod
    exclude:
      any:
      - resources:
          namespaces:
          - jenkins
          - kube-system
          - kyverno
    validate:
      message: "All images in this Pod must come from an authorized repository."
      pattern:
        spec:
          containers:
          - name: "*"
            image: "et02-harbor.ethiotelecom.net/* | docker.io/* | gcr.io/* | quay.io/*"
