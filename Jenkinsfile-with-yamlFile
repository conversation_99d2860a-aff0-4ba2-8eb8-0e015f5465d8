pipeline {
    agent {
        kubernetes {
            label 'agent'
            cloud 'kubernetes_mdc1'
            yamlFile 'k8s-pod-agent.yaml'
        }
    }

    environment {
        IMAGE = readMavenPom().getArtifactId()
        VERSION = "${BUILD_NUMBER}" // Use Jenkins build number as the version
        PP = sh(
            returnStdout: true,
            script: 'echo ${JOB_NAME} | cut -d/ -f2 | xargs echo -n'
        ).trim()
        LATEST_COMMIT = sh(
        script: 'git rev-parse HEAD',
        returnStdout: true
        ).trim()
        GIT_URL = sh(
        script: 'git config --get remote.origin.url | sed "s/\\.git$//"',
        returnStdout: true
        ).trim()
        COMMITTER_NAME = sh(
        script: 'git log -1 --pretty=format:"%an"',
        returnStdout: true
        ).trim()
        J_NAME = "${PP}-${BRANCH_NAME}:${VERSION}"
        DEPLOYMENT_FILE_DIR = './deployment'
        IMAGE_FULL_ADDR = 'et02-harbor.safaricomet.net/one_platform_it_delivery/oneplatform-onboardings-it-oat'
        MANIFEST_URL = 'technology/deploymentmanifest/one-platform-onboarding-dev3.git'
        TARGET_BRANCH = 'dev3'
        REPOSITORY_URL = 'https://gitlab.safaricomet.net/${MANIFEST_URL}'
    }

    stages {
        stage('Notify GitLab') {
            steps {
                echo 'Notifying GitLab'
                updateGitlabCommitStatus name: 'build', state: 'pending'
            }
        }

         stage("Build jar file"){
            steps{
                container('maven'){                    
                sh 'mvn -ntp -B -Dmaven.test.skip=true clean package'          
                }

            }
        }

        stage('Build Docker Image') {

            steps {
                container('kaniko') {
                    script {
                        sh '''
                            /kaniko/executor --dockerfile `pwd`/Dockerfile \
                            --context `pwd` \
                            --destination=et02-harbor.safaricomet.net/one_platform_it_delivery/${J_NAME}  --insecure --skip-tls-verify
                        '''
                    }
                }
            }
        }
        
        stage('Trigger ManifestUpdate') {
            steps {
                echo "triggering updatemanifestjob"
                build job: 'mpesa/manifest-updater', parameters: [string(name: 'IMAGE_TAG', value: "${VERSION}"), string(name: 'MANIFEST_URL', value: "${MANIFEST_URL}"), string(name: 'DEPLOYMENT_FILE_DIR', value: "${DEPLOYMENT_FILE_DIR}"), string(name: 'TARGET_BRANCH', value: "${TARGET_BRANCH}"), string(name: 'IMAGE_FULL_ADDR', value: "${IMAGE_FULL_ADDR}"), string(name: 'REPOSITORY_URL', value: "${REPOSITORY_URL}")]
            }

        }
    }
    
    post {
        always {
            updateGitlabCommitStatus name: 'build', state: 'success'
        }
        failure {
            updateGitlabCommitStatus name: 'build', state: 'failed'
        }
    }
}
