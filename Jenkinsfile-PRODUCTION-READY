pipeline {
    agent {
        kubernetes {
            cloud 'kubernetes_mdc1_robot_allure'
            label 'op-backend-agents-prod'
            idleMinutes 5
            defaultContainer 'jnlp'
            yaml """
apiVersion: v1
kind: Pod
metadata:
  name: k8-agent-containers
  namespace: jenkins
  labels:
    job: op-backend-agents
spec:
  serviceAccountName: jenkins-admin
  containers:
    - name: jnlp
      image: mdc1-sfcr.safaricomet.net/dev-tools/jenkins-inbound-agent:custom3
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "900m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: maven
      image: mdc1-sfcr.safaricomet.net/dev-tools/maven-builder:offline
      command: ["tail", "-f", "/dev/null"]
      env:
        - name: MAVEN_OPTS
          value: "-Xmx512m"
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "800Mi"
          cpu: "120m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
        - name: maven-cache
          mountPath: /root/.m2
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: kaniko
      image: mdc1-sfcr.safaricomet.net/dev-tools/kaniko-debug-local:offline
      command: ["cat"]
      tty: true
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "1"
      env:
        - name: DOCKER_CONFIG
          value: /etc/docker
      volumeMounts:
        - name: docker-config
          mountPath: /etc/docker
        - name: workspace-volume
          mountPath: /home/<USER>/agent
          readOnly: false
  imagePullSecrets:
    - name: harbor-registry-secret
  initContainers:
    - name: cleanup-container
      image: mdc1-sfcr.safaricomet.net/dev-tools/busybox:lts
      command: ["sh", "-c", "rm -rf /home/<USER>/agent/*"]
      resources:
        requests:
          memory: "64Mi"
          cpu: "10m"
        limits:
          memory: "128Mi"
          cpu: "50m"
      volumeMounts:
        - name: workspace-volume
          mountPath: /home/<USER>/agent
  volumes:
    - name: ca-cert
      configMap:
        name: safaricomet.net
        items:
          - key: ca-certificates.crt
            path: ca-certificates.crt
    - name: docker-config
      secret:
        secretName: harbor-registry-key
        items:
          - key: .dockerconfigjson
            path: config.json
    - name: maven-cache
      persistentVolumeClaim:
        claimName: maven-cache-pvc
    - name: workspace-volume
      emptyDir: {}
"""
        }
    }

    environment {
        VERSION = "${BUILD_NUMBER}"
        PP = sh(
            returnStdout: true,
            script: 'echo ${JOB_NAME} | cut -d/ -f2 | xargs echo -n'
        ).trim()
        DEPLOYMENT_FILE_DIR = './deployment'
        IMAGE_FULL_ADDR = 'et02-harbor.safaricomet.net/one_platform_it_delivery/oneplatform-onboardings-it-oat'
        MANIFEST_URL = 'technology/deploymentmanifest/one-platform-onboarding-dev3.git'
        TARGET_BRANCH = 'dev3'
        REPOSITORY_URL = 'https://gitlab.safaricomet.net/${MANIFEST_URL}'
    }

    stages {
        stage('Notify GitLab') {
            steps {
                echo 'Notifying GitLab'
                script {
                    try {
                        updateGitlabCommitStatus name: 'build', state: 'pending'
                    } catch (Exception e) {
                        echo "GitLab notification failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Validate Environment') {
            steps {
                echo 'Validating build environment...'
                sh 'echo "Jenkins agent is working!"'
                sh 'java -version'
                sh 'pwd && ls -la'
                
                container('maven') {
                    sh 'echo "Maven container is ready!"'
                    sh 'mvn --version'
                    sh 'java -version'
                }
            }
        }

        stage('Code Quality Check') {
            steps {
                echo 'Checking code structure...'
                sh 'find . -name "*.java" | wc -l | xargs echo "Java files found:"'
                sh 'find . -name "pom.xml" && echo "Maven configuration found"'
                sh 'find . -name "Dockerfile" && echo "Docker configuration found"'
                
                script {
                    if (fileExists('pom.xml')) {
                        def pom = readMavenPom file: 'pom.xml'
                        env.ARTIFACT_ID = pom.artifactId
                        env.VERSION_FROM_POM = pom.version
                        env.J_NAME = "${env.PP}-${env.BRANCH_NAME}:${env.VERSION}"
                        echo "Project: ${env.ARTIFACT_ID}"
                        echo "Version: ${env.VERSION_FROM_POM}"
                        echo "Image tag: ${env.J_NAME}"
                    }
                }
            }
        }

        stage('Build jar file') {
            when {
                expression { fileExists('pom.xml') }
            }
            steps {
                echo 'Building Java application with Maven...'
                container('maven') {
                    sh 'mvn -ntp -B -Dmaven.test.skip=true clean package'
                    sh 'ls -la target/'

                    script {
                        // Archive the built artifact
                        if (fileExists('target')) {
                            sh 'find target -name "*.jar" -type f'
                            echo "✅ Application built successfully!"
                            echo "📦 Artifact: ${env.ARTIFACT_ID}-${env.VERSION_FROM_POM}.jar"
                        }
                    }
                }
            }
        }

        stage('Build Docker Image') {
            when {
                expression { fileExists('Dockerfile') && fileExists('target') }
            }
            steps {
                echo 'Building Docker image with Kaniko...'
                container('kaniko') {
                    script {
                        sh """
                            /kaniko/executor \\
                                --dockerfile=\$(pwd)/Dockerfile \\
                                --context=\$(pwd) \\
                                --destination=et02-harbor.safaricomet.net/one_platform_it_delivery/${env.J_NAME} \\
                                --insecure \\
                                --skip-tls-verify
                        """
                        echo "✅ Docker image built and pushed successfully!"
                        echo "🐳 Image: et02-harbor.safaricomet.net/one_platform_it_delivery/${env.J_NAME}"
                    }
                }
            }
        }
        
        stage('Trigger ManifestUpdate') {
            when {
                expression { env.J_NAME != null }
            }
            steps {
                echo "triggering updatemanifestjob"
                script {
                    try {
                        build job: 'mpesa/manifest-updater', parameters: [
                            string(name: 'IMAGE_TAG', value: "${VERSION}"),
                            string(name: 'MANIFEST_URL', value: "${MANIFEST_URL}"),
                            string(name: 'DEPLOYMENT_FILE_DIR', value: "${DEPLOYMENT_FILE_DIR}"),
                            string(name: 'TARGET_BRANCH', value: "${TARGET_BRANCH}"),
                            string(name: 'IMAGE_FULL_ADDR', value: "${IMAGE_FULL_ADDR}"),
                            string(name: 'REPOSITORY_URL', value: "${REPOSITORY_URL}")
                        ]
                        echo "✅ Manifest update completed successfully!"
                    } catch (Exception e) {
                        echo "Manifest update failed: ${e.getMessage()}"
                    }
                }
            }
        }
    }
    
    post {
        always {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'success'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
        success {
            echo "🎉 Build completed successfully!"
            echo "✅ Java application compiled and packaged"
            echo "✅ Unit tests executed"
            echo "✅ Deployment package ready"
            echo "📋 Next steps: Add Docker build and deployment"
        }
        failure {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'failed'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
        unstable {
            echo "⚠️ Build completed with warnings (some tests may have failed)"
        }
    }
}