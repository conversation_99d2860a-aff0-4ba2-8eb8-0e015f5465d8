// Simple fix for your existing SSLUtils.createCustomSSLClient method
// Replace line 20 in your SSLUtils class with this:

// OLD CODE (line 20):
// InputStream trustStoreStream = new FileInputStream(keystorePath);

// NEW CODE:
String actualPath = keystorePath;
if (keystorePath.startsWith("file:")) {
    actualPath = keystorePath.substring(5); // Remove "file:" prefix
}
InputStream trustStoreStream = new FileInputStream(actualPath);
