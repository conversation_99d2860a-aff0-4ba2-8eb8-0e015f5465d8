pipeline {
    agent {
        kubernetes {
            yaml """
apiVersion: v1
kind: Pod
metadata:
  labels:
    jenkins: agent
spec:
  containers:
  - name: jnlp
    image: et02-harbor.ethiotelecom.net/jenkins/inbound-agent:latest
    resources:
      requests:
        memory: "256Mi"
        cpu: "100m"
      limits:
        memory: "1Gi"
        cpu: "1000m"
    env:
    - name: CONTAINER_ENV_VAR
      value: jnlp
  - name: maven
    image: et02-harbor.ethiotelecom.net/maven/maven:3.8.6-openjdk-17
    resources:
      requests:
        memory: "512Mi"
        cpu: "200m"
      limits:
        memory: "2Gi"
        cpu: "1500m"
    command:
    - cat
    tty: true
  - name: kaniko
    image: et02-harbor.ethiotelecom.net/kaniko-project/executor:debug
    resources:
      requests:
        memory: "512Mi"
        cpu: "200m"
      limits:
        memory: "2Gi"
        cpu: "1500m"
    command:
    - /busybox/cat
    tty: true
    volumeMounts:
    - name: kaniko-secret
      mountPath: /kaniko/.docker
  volumes:
  - name: kaniko-secret
    secret:
      secretName: harbor-registry-secret
      items:
      - key: .dockerconfigjson
        path: config.json
"""
        }
    }
    
    environment {
        HARBOR_REGISTRY = 'et02-harbor.ethiotelecom.net'
        HARBOR_PROJECT = 'it_delivery'
        IMAGE_NAME = 'oneplatform-onboardings-it'
        GITLAB_CREDENTIALS = 'gitlab-credentials'
    }
    
    stages {
        stage('Checkout') {
            steps {
                git credentialsId: "${GITLAB_CREDENTIALS}", 
                    url: 'https://gitlab.safaricomet.net/technology/one-platform/backend/oneplatform-onboardings-it.git',
                    branch: 'dev3'
            }
        }
        
        stage('Build') {
            steps {
                container('maven') {
                    sh '''
                        mvn clean compile -DskipTests
                        mvn package -DskipTests
                    '''
                }
            }
        }
        
        stage('Build & Push Docker Image') {
            steps {
                container('kaniko') {
                    script {
                        def imageTag = "${BUILD_NUMBER}"
                        sh """
                            /kaniko/executor \\
                                --dockerfile=Dockerfile \\
                                --context=. \\
                                --destination=${HARBOR_REGISTRY}/${HARBOR_PROJECT}/${IMAGE_NAME}:${imageTag} \\
                                --destination=${HARBOR_REGISTRY}/${HARBOR_PROJECT}/${IMAGE_NAME}:latest
                        """
                    }
                }
            }
        }
        
        stage('Update Manifest') {
            steps {
                container('jnlp') {
                    script {
                        def imageTag = "${BUILD_NUMBER}"
                        sh """
                            # Update Kubernetes manifest with new image tag
                            if [ -f k8s/deployment.yaml ]; then
                                sed -i 's|image: .*${IMAGE_NAME}.*|image: ${HARBOR_REGISTRY}/${HARBOR_PROJECT}/${IMAGE_NAME}:${imageTag}|g' k8s/deployment.yaml
                                git add k8s/deployment.yaml
                                git commit -m "Update image tag to ${imageTag}" || echo "No changes to commit"
                                git push origin dev3 || echo "Push failed"
                            else
                                echo "No deployment.yaml found"
                            fi
                        """
                    }
                }
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        success {
            echo 'Pipeline completed successfully!'
        }
        failure {
            echo 'Pipeline failed!'
        }
    }
}
