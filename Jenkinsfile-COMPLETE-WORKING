pipeline {
    agent {
        kubernetes {
            cloud 'kubernetes_mdc1_robot_allure'
            label 'op-backend-agents-full'
            idleMinutes 5
            defaultContainer 'jnlp'
            yaml """
apiVersion: v1
kind: Pod
metadata:
  name: k8-agent-containers
  namespace: jenkins
  labels:
    job: op-backend-agents
spec:
  serviceAccountName: jenkins-admin
  containers:
    - name: jnlp
      image: mdc1-sfcr.safaricomet.net/dev-tools/jenkins-inbound-agent:allure
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "900m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: maven
      image: mdc1-sfcr.safaricomet.net/dev-tools/maven-builder:offline
      command: ["tail", "-f", "/dev/null"]
      env:
        - name: MAVEN_OPTS
          value: "-Xmx512m"
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "800Mi"
          cpu: "120m"
      volumeMounts:
        - name: ca-cert
          mountPath: /etc/ssl/certs
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: kaniko
      image: mdc1-sfcr.safaricomet.net/dev-tools/kaniko-debug-local:offline
      command:
        - cat
      tty: true
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "1"
      env:
        - name: DOCKER_CONFIG
          value: /etc/docker
      volumeMounts:
        - name: docker-config
          mountPath: /etc/docker
        - name: workspace-volume
          mountPath: /home/<USER>/agent
          readOnly: false
  imagePullSecrets:
    - name: harbor-registry-secret
  volumes:
    - name: ca-cert
      configMap:
        name: safaricomet.net
        items:
          - key: ca-certificates.crt
            path: ca-certificates.crt
    - name: docker-config
      secret:
        secretName: harbor-registry-key
        items:
          - key: .dockerconfigjson
            path: config.json
    - name: workspace-volume
      emptyDir: {}
"""
        }
    }

    environment {
        VERSION = "${BUILD_NUMBER}"
        PP = sh(
            returnStdout: true,
            script: 'echo ${JOB_NAME} | cut -d/ -f2 | xargs echo -n'
        ).trim()
        DEPLOYMENT_FILE_DIR = './deployment'
        IMAGE_FULL_ADDR = 'et02-harbor.safaricomet.net/one_platform_it_delivery/oneplatform-onboardings-it-oat'
        MANIFEST_URL = 'technology/deploymentmanifest/one-platform-onboarding-dev3.git'
        TARGET_BRANCH = 'dev3'
        REPOSITORY_URL = 'https://gitlab.safaricomet.net/${MANIFEST_URL}'
    }

    stages {
        stage('Notify GitLab') {
            steps {
                echo 'Notifying GitLab'
                script {
                    try {
                        updateGitlabCommitStatus name: 'build', state: 'pending'
                    } catch (Exception e) {
                        echo "GitLab notification failed: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Check Repository') {
            steps {
                echo 'Checking repository contents...'
                sh 'pwd && ls -la'
                sh 'find . -name "*.java" | head -5 || echo "No Java files found"'
                sh 'find . -name "pom.xml" || echo "No pom.xml found"'
                sh 'find . -name "Dockerfile" || echo "No Dockerfile found"'
            }
        }

        stage('Read Maven POM') {
            steps {
                script {
                    if (fileExists('pom.xml')) {
                        def pom = readMavenPom file: 'pom.xml'
                        env.IMAGE_NAME = pom.artifactId
                        env.J_NAME = "${env.PP}-${env.BRANCH_NAME}:${env.VERSION}"
                        echo "Maven artifact ID: ${env.IMAGE_NAME}"
                        echo "Full image name: ${env.J_NAME}"
                    } else {
                        env.IMAGE_NAME = "oneplatform-onboardings-it"
                        env.J_NAME = "${env.PP}-${env.BRANCH_NAME}:${env.VERSION}"
                        echo "No pom.xml found, using default image name: ${env.IMAGE_NAME}"
                    }
                }
            }
        }

        stage('Test Containers') {
            steps {
                echo 'Testing container functionality...'
                
                container('jnlp') {
                    sh 'echo "JNLP container working!"'
                    sh 'java -version'
                }
                
                script {
                    try {
                        container('maven') {
                            sh 'echo "Maven container working!"'
                            sh 'mvn --version'
                        }
                    } catch (Exception e) {
                        echo "Maven container not ready: ${e.getMessage()}"
                    }
                    
                    try {
                        container('kaniko') {
                            sh 'echo "Kaniko container working!"'
                            sh 'ls -la /kaniko/ || echo "Kaniko directory not found"'
                        }
                    } catch (Exception e) {
                        echo "Kaniko container not ready: ${e.getMessage()}"
                    }
                }
            }
        }

        stage("Build jar file") {
            when {
                expression { fileExists('pom.xml') }
            }
            steps {
                container('maven') {                    
                    sh 'mvn -ntp -B -Dmaven.test.skip=true clean package'          
                }
            }
        }

        stage('Build Docker Image') {
            when {
                expression { fileExists('Dockerfile') }
            }
            steps {
                container('kaniko') {
                    script {
                        sh '''
                            /kaniko/executor --dockerfile `pwd`/Dockerfile \
                            --context `pwd` \
                            --destination=et02-harbor.safaricomet.net/one_platform_it_delivery/${J_NAME} --insecure --skip-tls-verify
                        '''
                    }
                }
            }
        }
        
        stage('Trigger ManifestUpdate') {
            when {
                expression { env.J_NAME != null }
            }
            steps {
                echo "triggering updatemanifestjob"
                script {
                    try {
                        build job: 'mpesa/manifest-updater', parameters: [
                            string(name: 'IMAGE_TAG', value: "${VERSION}"), 
                            string(name: 'MANIFEST_URL', value: "${MANIFEST_URL}"), 
                            string(name: 'DEPLOYMENT_FILE_DIR', value: "${DEPLOYMENT_FILE_DIR}"), 
                            string(name: 'TARGET_BRANCH', value: "${TARGET_BRANCH}"), 
                            string(name: 'IMAGE_FULL_ADDR', value: "${IMAGE_FULL_ADDR}"), 
                            string(name: 'REPOSITORY_URL', value: "${REPOSITORY_URL}")
                        ]
                    } catch (Exception e) {
                        echo "Manifest update failed: ${e.getMessage()}"
                    }
                }
            }
        }
    }
    
    post {
        always {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'success'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
        failure {
            script {
                try {
                    updateGitlabCommitStatus name: 'build', state: 'failed'
                } catch (Exception e) {
                    echo "GitLab notification failed: ${e.getMessage()}"
                }
            }
        }
    }
}
