pipeline {
    agent {
        kubernetes {
            yaml """
apiVersion: v1
kind: Pod
spec:
  containers:
    - name: jnlp
      image: jenkins/inbound-agent:latest-jdk17
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "1Gi"
          cpu: "500m"
"""
        }
    }

    stages {
        stage('Ultra Basic Test') {
            steps {
                echo 'Testing absolute minimal configuration...'
                sh 'echo "Hello from minimal Jenkins agent!"'
                sh 'java -version'
                sh 'pwd'
                sh 'whoami'
            }
        }
    }
}
