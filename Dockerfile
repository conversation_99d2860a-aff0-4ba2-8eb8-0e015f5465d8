FROM eclipse-temurin:17-jre-alpine

# Timezone
ENV TZ=Africa/Nairobi
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create app directory
WORKDIR /app

# Application metadata
LABEL name="Application-name" \
      description="Application description" \
      maintainer="maintainer of project"

# Copy truststore BEFORE switching user (important!)
COPY Truststore.jks /app/Truststore.jks

# Verify the file was copied and set proper permissions
RUN ls -la /app/Truststore.jks && \
    chmod 644 /app/Truststore.jks

# Add App
ARG JAR_FILE=target/*.jar
COPY ${JAR_FILE} /app/service.jar

# Set proper ownership for all files
RUN chown -R root:root /app && \
    chmod +x /app/service.jar

# Expose ports
EXPOSE 8383
EXPOSE 56555

# Verify files exist before starting (using ls instead of file command)
RUN echo "=== Verifying files ===" && \
    ls -la /app/ && \
    echo "=== Truststore verification ===" && \
    ls -la /app/Truststore.jks && \
    echo "=== Testing truststore access ===" && \
    test -r /app/Truststore.jks && echo "Truststore is readable" || echo "Truststore is NOT readable"

# Stay as root user
USER root

# Add JVM SSL configuration and start application
ENTRYPOINT ["java", \
    "-Djavax.net.ssl.trustStore=/app/Truststore.jks", \
    "-Djavax.net.ssl.trustStorePassword=changeit", \
    "-Djavax.net.ssl.trustStoreType=JKS", \
    "-Djava.security.debug=access:stack", \
    "-jar", "/app/service.jar"]
