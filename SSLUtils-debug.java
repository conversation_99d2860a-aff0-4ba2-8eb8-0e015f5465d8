// Debug version of SSLUtils to help identify the issue
public static CloseableHttpClient createCustomSSLClient(String keystorePath, String keystorePassword) {
    try {
        System.out.println("=== SSL Debug Info ===");
        System.out.println("Original keystorePath: " + keystorePath);
        
        // Handle file: protocol URLs
        String actualPath = keystorePath;
        if (keystorePath.startsWith("file:")) {
            actualPath = keystorePath.substring(5); // Remove "file:" prefix
            System.out.println("Removed file: prefix, actualPath: " + actualPath);
        }
        
        // Check if file exists
        java.io.File file = new java.io.File(actualPath);
        System.out.println("File exists: " + file.exists());
        System.out.println("File absolute path: " + file.getAbsolutePath());
        System.out.println("File can read: " + file.canRead());
        System.out.println("File length: " + file.length());
        
        if (!file.exists()) {
            // List directory contents
            java.io.File parentDir = file.getParentFile();
            if (parentDir != null && parentDir.exists()) {
                System.out.println("Parent directory contents:");
                String[] files = parentDir.list();
                if (files != null) {
                    for (String f : files) {
                        System.out.println("  - " + f);
                    }
                }
            }
            throw new RuntimeException("Truststore file not found: " + actualPath);
        }
        
        // Load the truststore
        KeyStore trustStore = KeyStore.getInstance("JKS");
        InputStream trustStoreStream = new FileInputStream(actualPath);
        trustStore.load(trustStoreStream, keystorePassword.toCharArray());
        trustStoreStream.close();
        
        System.out.println("Truststore loaded successfully");
        
        // Rest of your SSL context creation code...
        
    } catch (Exception e) {
        System.err.println("SSL Error: " + e.getMessage());
        e.printStackTrace();
        throw new RuntimeException("Failed to create custom SSL HttpClient", e);
    }
}
