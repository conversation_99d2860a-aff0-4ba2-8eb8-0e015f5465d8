pipeline {
    agent {
        kubernetes {
            label 'agent'
            cloud 'kubernetes_mdc1'
            yamlFile 'k8s-pod-agent.yaml'
        }
    }

    stages {
        stage('Test Pod Creation') {
            steps {
                echo 'Testing pod creation and container access'
                
                container('jnlp') {
                    sh 'echo "JNLP container is working"'
                    sh 'java -version'
                }
                
                container('maven') {
                    sh 'echo "Maven container is working"'
                    sh 'mvn --version'
                }
                
                container('kaniko') {
                    sh 'echo "Kaniko container is working"'
                    sh 'ls -la /kaniko/'
                }
            }
        }
    }
    
    post {
        always {
            echo 'Pipeline completed - pod template is working!'
        }
    }
}
