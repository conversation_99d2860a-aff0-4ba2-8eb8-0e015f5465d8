# Create Harbor registry secret for <PERSON> namespace
# Run this command to create the secret:

# Method 1: Using kubectl command
kubectl create secret docker-registry harbor-registry-secret \
  --docker-server=et02-harbor.ethiotelecom.net \
  --docker-username=YOUR_HARBOR_USERNAME \
  --docker-password=YOUR_HARBOR_PASSWORD \
  --docker-email=YOUR_EMAIL \
  --namespace=jenkins

# Method 2: Using YAML manifest
---
apiVersion: v1
kind: Secret
metadata:
  name: harbor-registry-secret
  namespace: jenkins
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: BASE64_ENCODED_DOCKER_CONFIG

# To generate the base64 encoded docker config:
# 1. Create a docker config file:
# {
#   "auths": {
#     "et02-harbor.ethiotelecom.net": {
#       "username": "YOUR_HARBOR_USERNAME",
#       "password": "YOUR_HARBOR_PASSWORD",
#       "email": "YOUR_EMAIL",
#       "auth": "BASE64_OF_USERNAME:PASSWORD"
#     }
#   }
# }
# 
# 2. Base64 encode the entire JSON:
# echo '{"auths":{"et02-harbor.ethiotelecom.net":{"username":"user","password":"pass","email":"email","auth":"************"}}}' | base64 -w 0

# Method 3: Using robot account (recommended for production)
kubectl create secret docker-registry harbor-registry-secret \
  --docker-server=et02-harbor.ethiotelecom.net \
  --docker-username=robot$it_delivery+jenkins \
  --docker-password=ROBOT_ACCOUNT_TOKEN \
  --docker-email=<EMAIL> \
  --namespace=jenkins
