package com.example.GetElecronicVocuherSerial.client;

import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ssl.SslBundle;
import org.springframework.boot.ssl.SslBundles;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClients;

@Component
public class SendSMS {

    private final RestTemplate restTemplate;
    
    @Value("${client.SMS.baseUrl}")
    private String baseUrl;
    
    @Value("${client.SMS.uri}")
    private String uri;
    
    @Value("${client.sms.authPassword}")
    private String authPassword;
    
    @Value("${client.sms.senderId}")
    private String senderId;

    // Constructor using Spring's SSL Bundle (Spring Boot 3.x way)
    public SendSMS(SslBundles sslBundles, RestTemplateBuilder restTemplateBuilder) {
        try {
            // Get the SSL bundle
            SslBundle sslBundle = sslBundles.getBundle("clientbundle");
            SSLContext sslContext = sslBundle.createSslContext();
            
            // Create SSL socket factory
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    new String[]{"TLSv1.2", "TLSv1.3"},
                    null,
                    SSLConnectionSocketFactory.getDefaultHostnameVerifier()
            );

            // Create HTTP client with custom SSL
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLSocketFactory(sslSocketFactory)
                    .build();

            // Create RestTemplate with custom HTTP client
            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
            this.restTemplate = restTemplateBuilder
                    .requestFactory(() -> factory)
                    .build();
                    
        } catch (Exception e) {
            throw new RuntimeException("Failed to create SSL-enabled RestTemplate", e);
        }
    }

    // Alternative constructor fallback (if SSL bundle is not available)
    public SendSMS(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder.build();
    }

    // Your SMS sending methods here...
    public void sendSMS(String message, String recipient) {
        // Implementation for sending SMS
        String fullUrl = baseUrl + uri;
        // ... rest of your SMS logic
    }
}
