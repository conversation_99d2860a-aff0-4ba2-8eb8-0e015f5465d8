apiVersion: v1
kind: Pod
metadata:
  name: k8-agent-containers
  namespace: jenkins
  labels:
    job: op-backend-agents
spec:
  serviceAccountName: jenkins-admin
  containers:
    - name: jnlp
      image: mdc1-sfcr.safaricomet.net/qa-tools/jenkins-inbound-agent:custom3
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "900m"
      volumeMounts:
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: maven
      image: mdc1-sfcr.safaricomet.net/qa-tools/maven-builder:offline
      command: ["tail", "-f", "/dev/null"]
      env:
        - name: MAVEN_OPTS
          value: "-Xmx512m"
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "800Mi"
          cpu: "120m"
      volumeMounts:
        - name: workspace-volume
          mountPath: /home/<USER>/agent
    - name: kaniko
      image: mdc1-sfcr.safaricomet.net/qa-tools/kaniko-debug-local:offline
      command:
        - cat
      tty: true
      imagePullPolicy: Always
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "1"
      env:
        - name: DOCKER_CONFIG
          value: /etc/docker
      volumeMounts:
        - name: docker-config
          mountPath: /etc/docker
        - name: workspace-volume
          mountPath: /home/<USER>/agent
          readOnly: false
    - name: sonarcli
      image: mdc1-sfcr.safaricomet.net/qa-tools/sonar-scanner-cli:offline
      imagePullPolicy: Always
      command: ["tail", "-f", "/dev/null"]
      resources:
        requests:
          memory: "256Mi"
          cpu: "80m"
        limits:
          memory: "2Gi"
          cpu: "900m"
      volumeMounts:
        - name: workspace-volume
          mountPath: /home/<USER>/agent
          readOnly: false
    - name: kubectl
      image: mdc1-sfcr.safaricomet.net/qa-tools/kubectl:latest-local
      securityContext:
        runAsUser: 0
        runAsGroup: 0
      resources:
        requests:
          memory: "256Mi"
          cpu: "50m"
        limits:
          memory: "2Gi"
          cpu: "800m"
      command:
        - cat
      tty: true
      volumeMounts:
        - name: workspace-volume
          mountPath: /home/<USER>/agent
          readOnly: false
  imagePullSecrets:
    - name: harbor-registry-secret
  volumes:
    - name: docker-config
      secret:
        secretName: harbor-registry-key
        items:
          - key: .dockerconfigjson
            path: config.json
    - name: workspace-volume
      emptyDir: {}
