FROM eclipse-temurin:17-jre-alpine

# Timezone
ENV TZ=Africa/Nairobi
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install additional tools for debugging (optional)
RUN apk add --no-cache curl openssl

# Create app directory
WORKDIR /app

# Application metadata
LABEL name="Application-name" \
      description="Application description" \
      maintainer="maintainer of project"

# Copy truststore
COPY Truststore.jks /app/Truststore.jks

# Verify and set permissions
RUN ls -la /app/Truststore.jks && \
    chmod 644 /app/Truststore.jks && \
    echo "=== Testing keystore with keytool ===" && \
    keytool -list -keystore /app/Truststore.jks -storepass changeit -v | head -20

# Add App
ARG JAR_FILE=target/*.jar
COPY ${JAR_FILE} /app/service.jar

# Set proper ownership
RUN chown -R root:root /app && \
    chmod +x /app/service.jar

# Expose ports
EXPOSE 8383
EXPOSE 56555

# Verify everything
RUN echo "=== Final verification ===" && \
    ls -la /app/ && \
    echo "=== Java version ===" && \
    java -version

USER root

# Enhanced SSL debugging
ENTRYPOINT ["java", \
    "-Djavax.net.ssl.trustStore=/app/Truststore.jks", \
    "-Djavax.net.ssl.trustStorePassword=changeit", \
    "-Djavax.net.ssl.trustStoreType=JKS", \
    "-Djavax.net.debug=ssl,handshake,data,trustmanager", \
    "-Djava.security.debug=access:stack", \
    "-Dcom.sun.net.ssl.checkRevocation=false", \
    "-jar", "/app/service.jar"]
